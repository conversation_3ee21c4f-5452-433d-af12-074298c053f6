"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(app-pages-browser)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Dashboard Enhancements\n            \"welcome\": \"مرحباً\",\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"system_overview\": \"النظام\",\n            \"your_requests\": \"طلباتك\",\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n            \"pending_applications\": \"الطلبات المعلقة\",\n            \"active_distributions\": \"التوزيعات النشطة\",\n            \"quick_actions\": \"الإجراءات السريعة\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"search_beneficiaries\": \"البحث عن المستفيدين\",\n            \"generate_report\": \"إنشاء تقرير\",\n            \"manage_distributions\": \"إدارة التوزيعات\",\n            \"recent_activity\": \"النشاط الأخير\",\n            \"distribution_overview\": \"نظرة عامة على التوزيع\",\n            \"monthly_distributions\": \"التوزيعات الشهرية\",\n            \"beneficiary_categories\": \"فئات المستفيدين\",\n            \"view_all\": \"عرض الكل\",\n            \"this_month\": \"هذا الشهر\",\n            \"last_month\": \"الشهر الماضي\",\n            \"last_week\": \"الأسبوع الماضي\",\n            \"this_week\": \"هذا الأسبوع\",\n            \"increase\": \"زيادة\",\n            \"decrease\": \"انخفاض\",\n            \"from\": \"من\",\n            \"sar\": \"ريال سعودي\",\n            \"beneficiaries\": \"مستفيدين\",\n            \"applications\": \"طلبات\",\n            \"distributions\": \"توزيعات\",\n            \"completed\": \"مكتمل\",\n            \"pending\": \"معلق\",\n            \"warning\": \"تحذير\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Admin Navigation\n            \"User Management\": \"إدارة المستخدمين\",\n            \"Assistance Types\": \"أنواع المساعدات\",\n            \"System Settings\": \"إعدادات النظام\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"total_received\": \"إجمالي المستلم\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Dashboard Enhancements\n            \"welcome\": \"Welcome\",\n            \"overview_status\": \"Overview of\",\n            \"system_overview\": \"system status\",\n            \"your_requests\": \"your requests\",\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"total_zakat_distributed\": \"Total Zakat Distributed\",\n            \"pending_applications\": \"Pending Applications\",\n            \"active_distributions\": \"Active Distributions\",\n            \"quick_actions\": \"Quick Actions\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"search_beneficiaries\": \"Search Beneficiaries\",\n            \"generate_report\": \"Generate Report\",\n            \"manage_distributions\": \"Manage Distributions\",\n            \"recent_activity\": \"Recent Activity\",\n            \"distribution_overview\": \"Distribution Overview\",\n            \"monthly_distributions\": \"Monthly Distributions\",\n            \"beneficiary_categories\": \"Beneficiary Categories\",\n            \"view_all\": \"View All\",\n            \"this_month\": \"This Month\",\n            \"last_month\": \"Last Month\",\n            \"last_week\": \"Last Week\",\n            \"this_week\": \"This Week\",\n            \"increase\": \"increase\",\n            \"decrease\": \"decrease\",\n            \"from\": \"from\",\n            \"sar\": \"SAR\",\n            \"beneficiaries\": \"beneficiaries\",\n            \"applications\": \"applications\",\n            \"distributions\": \"distributions\",\n            \"completed\": \"Completed\",\n            \"pending\": \"Pending\",\n            \"warning\": \"Warning\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"total_received\": \"Total Received\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});