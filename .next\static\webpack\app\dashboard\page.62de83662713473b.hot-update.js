"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/recent-activity.tsx":
/*!**************************************************!*\
  !*** ./components/dashboard/recent-activity.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentActivity: function() { return /* binding */ RecentActivity; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/en-US.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ RecentActivity auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mock data - replace with real data from your mock-data.ts\nconst getMockActivities = (t)=>[\n        {\n            id: \"1\",\n            type: \"registration\",\n            title: t(\"New beneficiary registered\"),\n            description: t(\"Ahmed Mohammed Al-Rashid has been registered\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 30),\n            status: \"completed\",\n            user: t(\"Reception Staff\")\n        },\n        {\n            id: \"2\",\n            type: \"approval\",\n            title: t(\"Application approved\"),\n            description: t(\"Fatima Al-Zahra application approved for Zakat distribution\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n            status: \"completed\",\n            user: t(\"Case Manager\")\n        },\n        {\n            id: \"3\",\n            type: \"distribution\",\n            title: t(\"Zakat distributed\"),\n            description: t(\"5,000 SAR distributed to 10 beneficiaries\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),\n            status: \"completed\",\n            user: t(\"Finance Manager\")\n        },\n        {\n            id: \"4\",\n            type: \"review\",\n            title: t(\"Pending review\"),\n            description: t(\"3 applications require case manager review\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),\n            status: \"pending\",\n            user: t(\"System\")\n        }\n    ];\nfunction RecentActivity() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const isArabic = i18n.language === \"ar\";\n    const mockActivities = getMockActivities(t);\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"registration\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"approval\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"distribution\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"review\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n            default:\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"default\";\n            case \"pending\":\n                return \"secondary\";\n            case \"warning\":\n                return \"destructive\";\n            default:\n                return \"secondary\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"card-enhanced border-0 bg-card/80 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                className: \"flex flex-row items-center justify-between pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-xl font-semibold flex items-center space-x-2 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 w-8 rounded-lg bg-gradient-success flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 text-success-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t(\"recent_activity\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"text-primary hover:text-primary-light hover:bg-primary/10\",\n                        children: t(\"view_all\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                lineNumber: 93,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: mockActivities.map((activity)=>{\n                        const Icon = getActivityIcon(activity.type);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group flex items-start space-x-3 rtl:space-x-reverse p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                        lineNumber: 111,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-semibold text-foreground\",\n                                                    children: activity.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    variant: getStatusColor(activity.status),\n                                                    className: `text-xs font-medium ${activity.status === \"completed\" ? \"bg-success/20 text-success border-success/30\" : activity.status === \"pending\" ? \"bg-warning/20 text-warning border-warning/30\" : \"bg-destructive/20 text-destructive border-destructive/30\"}`,\n                                                    children: t(activity.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-2\",\n                                            children: activity.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(activity.timestamp, {\n                                                    addSuffix: true,\n                                                    locale: isArabic ? date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.ar : date_fns_locale__WEBPACK_IMPORTED_MODULE_12__.enUS\n                                                }),\n                                                activity.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-1 text-primary\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 23\n                                                }, this),\n                                                activity.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: activity.user\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, activity.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                lineNumber: 104,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n        lineNumber: 92,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentActivity, \"OZwazanA59tbNDUkc8lMSmTHj9Q=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = RecentActivity;\nvar _c;\n$RefreshReg$(_c, \"RecentActivity\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvZGFzaGJvYXJkL3JlY2VudC1hY3Rpdml0eS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUUrRTtBQUNsQztBQUNFO0FBQ0Q7QUFDQTtBQUNKO0FBT3JCO0FBWXJCLDREQUE0RDtBQUM1RCxNQUFNZSxvQkFBb0IsQ0FBQ0MsSUFBMkI7UUFDcEQ7WUFDRUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE9BQU9ILEVBQUU7WUFDVEksYUFBYUosRUFBRTtZQUNmSyxXQUFXLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxPQUFPLEtBQUs7WUFDN0NDLFFBQVE7WUFDUkMsTUFBTVQsRUFBRTtRQUNWO1FBQ0E7WUFDRUMsSUFBSTtZQUNKQyxNQUFNO1lBQ05DLE9BQU9ILEVBQUU7WUFDVEksYUFBYUosRUFBRTtZQUNmSyxXQUFXLElBQUlDLEtBQUtBLEtBQUtDLEdBQUcsS0FBSyxPQUFPLEtBQUssS0FBSztZQUNsREMsUUFBUTtZQUNSQyxNQUFNVCxFQUFFO1FBQ1Y7UUFDQTtZQUNFQyxJQUFJO1lBQ0pDLE1BQU07WUFDTkMsT0FBT0gsRUFBRTtZQUNUSSxhQUFhSixFQUFFO1lBQ2ZLLFdBQVcsSUFBSUMsS0FBS0EsS0FBS0MsR0FBRyxLQUFLLE9BQU8sS0FBSyxLQUFLO1lBQ2xEQyxRQUFRO1lBQ1JDLE1BQU1ULEVBQUU7UUFDVjtRQUNBO1lBQ0VDLElBQUk7WUFDSkMsTUFBTTtZQUNOQyxPQUFPSCxFQUFFO1lBQ1RJLGFBQWFKLEVBQUU7WUFDZkssV0FBVyxJQUFJQyxLQUFLQSxLQUFLQyxHQUFHLEtBQUssT0FBTyxLQUFLLEtBQUs7WUFDbERDLFFBQVE7WUFDUkMsTUFBTVQsRUFBRTtRQUNWO0tBQ0Q7QUFFTSxTQUFTVTs7SUFDZCxNQUFNLEVBQUVWLENBQUMsRUFBRVcsSUFBSSxFQUFFLEdBQUdyQiw2REFBY0E7SUFDbEMsTUFBTXNCLFdBQVdELEtBQUtFLFFBQVEsS0FBSztJQUNuQyxNQUFNQyxpQkFBaUJmLGtCQUFrQkM7SUFFekMsTUFBTWUsa0JBQWtCLENBQUNiO1FBQ3ZCLE9BQVFBO1lBQ04sS0FBSztnQkFBZ0IsT0FBT1IsNkhBQVFBO1lBQ3BDLEtBQUs7Z0JBQVksT0FBT0csNkhBQVdBO1lBQ25DLEtBQUs7Z0JBQWdCLE9BQU9GLDZIQUFVQTtZQUN0QyxLQUFLO2dCQUFVLE9BQU9DLDZIQUFXQTtZQUNqQztnQkFBUyxPQUFPRSw2SEFBS0E7UUFDdkI7SUFDRjtJQUVBLE1BQU1rQixpQkFBaUIsQ0FBQ1I7UUFDdEIsT0FBUUE7WUFDTixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3hCLHFEQUFJQTtRQUFDaUMsV0FBVTs7MEJBQ2QsOERBQUMvQiwyREFBVUE7Z0JBQUMrQixXQUFVOztrQ0FDcEIsOERBQUM5QiwwREFBU0E7d0JBQUM4QixXQUFVOzswQ0FDbkIsOERBQUNDO2dDQUFJRCxXQUFVOzBDQUNiLDRFQUFDbkIsNkhBQUtBO29DQUFDbUIsV0FBVTs7Ozs7Ozs7Ozs7MENBRW5CLDhEQUFDRTswQ0FBTW5CLEVBQUU7Ozs7Ozs7Ozs7OztrQ0FFWCw4REFBQ1gseURBQU1BO3dCQUFDK0IsU0FBUTt3QkFBUUMsTUFBSzt3QkFBS0osV0FBVTtrQ0FDekNqQixFQUFFOzs7Ozs7Ozs7Ozs7MEJBR1AsOERBQUNmLDREQUFXQTswQkFDViw0RUFBQ2lDO29CQUFJRCxXQUFVOzhCQUNaSCxlQUFlUSxHQUFHLENBQUMsQ0FBQ0M7d0JBQ25CLE1BQU1DLE9BQU9ULGdCQUFnQlEsU0FBU3JCLElBQUk7d0JBQzFDLHFCQUNFLDhEQUFDZ0I7NEJBQXNCRCxXQUFVOzs4Q0FDL0IsOERBQUNDO29DQUFJRCxXQUFVOzhDQUNiLDRFQUFDQzt3Q0FBSUQsV0FBVTtrREFDYiw0RUFBQ087NENBQUtQLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7OENBR3BCLDhEQUFDQztvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNDOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ1E7b0RBQUVSLFdBQVU7OERBQXlDTSxTQUFTcEIsS0FBSzs7Ozs7OzhEQUNwRSw4REFBQ2YsdURBQUtBO29EQUNKZ0MsU0FBU0osZUFBZU8sU0FBU2YsTUFBTTtvREFDdkNTLFdBQVcsQ0FBQyxvQkFBb0IsRUFDOUJNLFNBQVNmLE1BQU0sS0FBSyxjQUNoQixpREFDQWUsU0FBU2YsTUFBTSxLQUFLLFlBQ3BCLGlEQUNBLDJEQUNMLENBQUM7OERBRURSLEVBQUV1QixTQUFTZixNQUFNOzs7Ozs7Ozs7Ozs7c0RBR3RCLDhEQUFDaUI7NENBQUVSLFdBQVU7c0RBQXNDTSxTQUFTbkIsV0FBVzs7Ozs7O3NEQUN2RSw4REFBQ3FCOzRDQUFFUixXQUFVOztnREFDVjFCLHlHQUFtQkEsQ0FBQ2dDLFNBQVNsQixTQUFTLEVBQUU7b0RBQ3ZDcUIsV0FBVztvREFDWEMsUUFBUWYsV0FBV3BCLGdEQUFFQSxHQUFHQyxrREFBSUE7Z0RBQzlCO2dEQUNDOEIsU0FBU2QsSUFBSSxrQkFDWiw4REFBQ1U7b0RBQUtGLFdBQVU7OERBQW9COzs7Ozs7Z0RBRXJDTSxTQUFTZCxJQUFJLGtCQUNaLDhEQUFDVTtvREFBS0YsV0FBVTs4REFBZU0sU0FBU2QsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7OzsyQkFoQzFDYyxTQUFTdEIsRUFBRTs7Ozs7b0JBc0N6Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLVjtHQXJGZ0JTOztRQUNNcEIseURBQWNBOzs7S0FEcEJvQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL2Rhc2hib2FyZC9yZWNlbnQtYWN0aXZpdHkudHN4P2MwNWEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9uIH0gZnJvbSAncmVhY3QtaTE4bmV4dCdcbmltcG9ydCB7IGZvcm1hdERpc3RhbmNlVG9Ob3cgfSBmcm9tICdkYXRlLWZucydcbmltcG9ydCB7IGFyLCBlblVTIH0gZnJvbSAnZGF0ZS1mbnMvbG9jYWxlJ1xuaW1wb3J0IHtcbiAgVXNlclBsdXMsXG4gIENyZWRpdENhcmQsXG4gIEFsZXJ0Q2lyY2xlLFxuICBDaGVja0NpcmNsZSxcbiAgQ2xvY2tcbn0gZnJvbSAnbHVjaWRlLXJlYWN0J1xuXG5pbnRlcmZhY2UgQWN0aXZpdHlJdGVtIHtcbiAgaWQ6IHN0cmluZ1xuICB0eXBlOiAncmVnaXN0cmF0aW9uJyB8ICdhcHByb3ZhbCcgfCAnZGlzdHJpYnV0aW9uJyB8ICdyZXZpZXcnXG4gIHRpdGxlOiBzdHJpbmdcbiAgZGVzY3JpcHRpb246IHN0cmluZ1xuICB0aW1lc3RhbXA6IERhdGVcbiAgc3RhdHVzOiAnY29tcGxldGVkJyB8ICdwZW5kaW5nJyB8ICd3YXJuaW5nJ1xuICB1c2VyPzogc3RyaW5nXG59XG5cbi8vIE1vY2sgZGF0YSAtIHJlcGxhY2Ugd2l0aCByZWFsIGRhdGEgZnJvbSB5b3VyIG1vY2stZGF0YS50c1xuY29uc3QgZ2V0TW9ja0FjdGl2aXRpZXMgPSAodDogYW55KTogQWN0aXZpdHlJdGVtW10gPT4gW1xuICB7XG4gICAgaWQ6ICcxJyxcbiAgICB0eXBlOiAncmVnaXN0cmF0aW9uJyxcbiAgICB0aXRsZTogdCgnTmV3IGJlbmVmaWNpYXJ5IHJlZ2lzdGVyZWQnKSxcbiAgICBkZXNjcmlwdGlvbjogdCgnQWhtZWQgTW9oYW1tZWQgQWwtUmFzaGlkIGhhcyBiZWVuIHJlZ2lzdGVyZWQnKSxcbiAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAxMDAwICogNjAgKiAzMCksIC8vIDMwIG1pbnV0ZXMgYWdvXG4gICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICB1c2VyOiB0KCdSZWNlcHRpb24gU3RhZmYnKVxuICB9LFxuICB7XG4gICAgaWQ6ICcyJyxcbiAgICB0eXBlOiAnYXBwcm92YWwnLFxuICAgIHRpdGxlOiB0KCdBcHBsaWNhdGlvbiBhcHByb3ZlZCcpLFxuICAgIGRlc2NyaXB0aW9uOiB0KCdGYXRpbWEgQWwtWmFocmEgYXBwbGljYXRpb24gYXBwcm92ZWQgZm9yIFpha2F0IGRpc3RyaWJ1dGlvbicpLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEwMDAgKiA2MCAqIDYwICogMiksIC8vIDIgaG91cnMgYWdvXG4gICAgc3RhdHVzOiAnY29tcGxldGVkJyxcbiAgICB1c2VyOiB0KCdDYXNlIE1hbmFnZXInKVxuICB9LFxuICB7XG4gICAgaWQ6ICczJyxcbiAgICB0eXBlOiAnZGlzdHJpYnV0aW9uJyxcbiAgICB0aXRsZTogdCgnWmFrYXQgZGlzdHJpYnV0ZWQnKSxcbiAgICBkZXNjcmlwdGlvbjogdCgnNSwwMDAgU0FSIGRpc3RyaWJ1dGVkIHRvIDEwIGJlbmVmaWNpYXJpZXMnKSxcbiAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKERhdGUubm93KCkgLSAxMDAwICogNjAgKiA2MCAqIDQpLCAvLyA0IGhvdXJzIGFnb1xuICAgIHN0YXR1czogJ2NvbXBsZXRlZCcsXG4gICAgdXNlcjogdCgnRmluYW5jZSBNYW5hZ2VyJylcbiAgfSxcbiAge1xuICAgIGlkOiAnNCcsXG4gICAgdHlwZTogJ3JldmlldycsXG4gICAgdGl0bGU6IHQoJ1BlbmRpbmcgcmV2aWV3JyksXG4gICAgZGVzY3JpcHRpb246IHQoJzMgYXBwbGljYXRpb25zIHJlcXVpcmUgY2FzZSBtYW5hZ2VyIHJldmlldycpLFxuICAgIHRpbWVzdGFtcDogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDEwMDAgKiA2MCAqIDYwICogNiksIC8vIDYgaG91cnMgYWdvXG4gICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgdXNlcjogdCgnU3lzdGVtJylcbiAgfVxuXVxuXG5leHBvcnQgZnVuY3Rpb24gUmVjZW50QWN0aXZpdHkoKSB7XG4gIGNvbnN0IHsgdCwgaTE4biB9ID0gdXNlVHJhbnNsYXRpb24oKVxuICBjb25zdCBpc0FyYWJpYyA9IGkxOG4ubGFuZ3VhZ2UgPT09ICdhcidcbiAgY29uc3QgbW9ja0FjdGl2aXRpZXMgPSBnZXRNb2NrQWN0aXZpdGllcyh0KVxuXG4gIGNvbnN0IGdldEFjdGl2aXR5SWNvbiA9ICh0eXBlOiBBY3Rpdml0eUl0ZW1bJ3R5cGUnXSkgPT4ge1xuICAgIHN3aXRjaCAodHlwZSkge1xuICAgICAgY2FzZSAncmVnaXN0cmF0aW9uJzogcmV0dXJuIFVzZXJQbHVzXG4gICAgICBjYXNlICdhcHByb3ZhbCc6IHJldHVybiBDaGVja0NpcmNsZVxuICAgICAgY2FzZSAnZGlzdHJpYnV0aW9uJzogcmV0dXJuIENyZWRpdENhcmRcbiAgICAgIGNhc2UgJ3Jldmlldyc6IHJldHVybiBBbGVydENpcmNsZVxuICAgICAgZGVmYXVsdDogcmV0dXJuIENsb2NrXG4gICAgfVxuICB9XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoc3RhdHVzOiBBY3Rpdml0eUl0ZW1bJ3N0YXR1cyddKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6IHJldHVybiAnZGVmYXVsdCdcbiAgICAgIGNhc2UgJ3BlbmRpbmcnOiByZXR1cm4gJ3NlY29uZGFyeSdcbiAgICAgIGNhc2UgJ3dhcm5pbmcnOiByZXR1cm4gJ2Rlc3RydWN0aXZlJ1xuICAgICAgZGVmYXVsdDogcmV0dXJuICdzZWNvbmRhcnknXG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZCBjbGFzc05hbWU9XCJjYXJkLWVuaGFuY2VkIGJvcmRlci0wIGJnLWNhcmQvODAgYmFja2Ryb3AtYmx1ci1zbVwiPlxuICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHBiLTRcIj5cbiAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0yIHJ0bDpzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtOCB3LTggcm91bmRlZC1sZyBiZy1ncmFkaWVudC1zdWNjZXNzIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XG4gICAgICAgICAgICA8Q2xvY2sgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXN1Y2Nlc3MtZm9yZWdyb3VuZFwiIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPHNwYW4+e3QoJ3JlY2VudF9hY3Rpdml0eScpfTwvc3Bhbj5cbiAgICAgICAgPC9DYXJkVGl0bGU+XG4gICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCIgY2xhc3NOYW1lPVwidGV4dC1wcmltYXJ5IGhvdmVyOnRleHQtcHJpbWFyeS1saWdodCBob3ZlcjpiZy1wcmltYXJ5LzEwXCI+XG4gICAgICAgICAge3QoJ3ZpZXdfYWxsJyl9XG4gICAgICAgIDwvQnV0dG9uPlxuICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgIHttb2NrQWN0aXZpdGllcy5tYXAoKGFjdGl2aXR5KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBJY29uID0gZ2V0QWN0aXZpdHlJY29uKGFjdGl2aXR5LnR5cGUpXG4gICAgICAgICAgICByZXR1cm4gKFxuICAgICAgICAgICAgICA8ZGl2IGtleT17YWN0aXZpdHkuaWR9IGNsYXNzTmFtZT1cImdyb3VwIGZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zIHJ0bDpzcGFjZS14LXJldmVyc2UgcC0zIHJvdW5kZWQtbGcgaG92ZXI6YmctYWNjZW50LzUwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMTAgdy0xMCByb3VuZGVkLWZ1bGwgYmctcHJpbWFyeS8xMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBncm91cC1ob3ZlcjpiZy1wcmltYXJ5LzIwIHRyYW5zaXRpb24tY29sb3JzXCI+XG4gICAgICAgICAgICAgICAgICAgIDxJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC1wcmltYXJ5XCIgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIG1pbi13LTBcIj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIG1iLTFcIj5cbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LXNlbWlib2xkIHRleHQtZm9yZWdyb3VuZFwiPnthY3Rpdml0eS50aXRsZX08L3A+XG4gICAgICAgICAgICAgICAgICAgIDxCYWRnZVxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9e2dldFN0YXR1c0NvbG9yKGFjdGl2aXR5LnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgdGV4dC14cyBmb250LW1lZGl1bSAke1xuICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZpdHkuc3RhdHVzID09PSAnY29tcGxldGVkJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy1zdWNjZXNzLzIwIHRleHQtc3VjY2VzcyBib3JkZXItc3VjY2Vzcy8zMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiBhY3Rpdml0eS5zdGF0dXMgPT09ICdwZW5kaW5nJ1xuICAgICAgICAgICAgICAgICAgICAgICAgICA/ICdiZy13YXJuaW5nLzIwIHRleHQtd2FybmluZyBib3JkZXItd2FybmluZy8zMCdcbiAgICAgICAgICAgICAgICAgICAgICAgICAgOiAnYmctZGVzdHJ1Y3RpdmUvMjAgdGV4dC1kZXN0cnVjdGl2ZSBib3JkZXItZGVzdHJ1Y3RpdmUvMzAnXG4gICAgICAgICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICB7dChhY3Rpdml0eS5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICA8L0JhZGdlPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi0yXCI+e2FjdGl2aXR5LmRlc2NyaXB0aW9ufTwvcD5cbiAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICAgIHtmb3JtYXREaXN0YW5jZVRvTm93KGFjdGl2aXR5LnRpbWVzdGFtcCwge1xuICAgICAgICAgICAgICAgICAgICAgIGFkZFN1ZmZpeDogdHJ1ZSxcbiAgICAgICAgICAgICAgICAgICAgICBsb2NhbGU6IGlzQXJhYmljID8gYXIgOiBlblVTXG4gICAgICAgICAgICAgICAgICAgIH0pfVxuICAgICAgICAgICAgICAgICAgICB7YWN0aXZpdHkudXNlciAmJiAoXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXgtMSB0ZXh0LXByaW1hcnlcIj7igKI8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgIHthY3Rpdml0eS51c2VyICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPnthY3Rpdml0eS51c2VyfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApXG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9DYXJkQ29udGVudD5cbiAgICA8L0NhcmQ+XG4gIClcbn0iXSwibmFtZXMiOlsiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIkJhZGdlIiwiQnV0dG9uIiwidXNlVHJhbnNsYXRpb24iLCJmb3JtYXREaXN0YW5jZVRvTm93IiwiYXIiLCJlblVTIiwiVXNlclBsdXMiLCJDcmVkaXRDYXJkIiwiQWxlcnRDaXJjbGUiLCJDaGVja0NpcmNsZSIsIkNsb2NrIiwiZ2V0TW9ja0FjdGl2aXRpZXMiLCJ0IiwiaWQiLCJ0eXBlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInRpbWVzdGFtcCIsIkRhdGUiLCJub3ciLCJzdGF0dXMiLCJ1c2VyIiwiUmVjZW50QWN0aXZpdHkiLCJpMThuIiwiaXNBcmFiaWMiLCJsYW5ndWFnZSIsIm1vY2tBY3Rpdml0aWVzIiwiZ2V0QWN0aXZpdHlJY29uIiwiZ2V0U3RhdHVzQ29sb3IiLCJjbGFzc05hbWUiLCJkaXYiLCJzcGFuIiwidmFyaWFudCIsInNpemUiLCJtYXAiLCJhY3Rpdml0eSIsIkljb24iLCJwIiwiYWRkU3VmZml4IiwibG9jYWxlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/recent-activity.tsx\n"));

/***/ })

});