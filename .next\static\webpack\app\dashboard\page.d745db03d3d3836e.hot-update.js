"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/quick-actions.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/quick-actions.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QuickActions: function() { return /* binding */ QuickActions; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(app-pages-browser)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Search,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Search,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Search,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Search,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Search,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CreditCard,FileText,Search,Settings,UserPlus,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ QuickActions auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction QuickActions() {\n    _s();\n    const { t } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation)();\n    const { data: session } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const actions = [\n        {\n            title: t(\"register_new_beneficiary\"),\n            description: t(\"Add a new beneficiary to the system\"),\n            icon: _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/beneficiaries/register\",\n            roles: [\n                \"reception_staff\",\n                \"case_manager\",\n                \"system_admin\"\n            ]\n        },\n        {\n            title: t(\"search_beneficiaries\"),\n            description: t(\"Find and manage existing beneficiaries\"),\n            icon: _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/beneficiaries\",\n            roles: [\n                \"reception_staff\",\n                \"case_manager\",\n                \"system_admin\",\n                \"finance_manager\"\n            ]\n        },\n        {\n            title: t(\"generate_report\"),\n            description: t(\"Create distribution and analytics reports\"),\n            icon: _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/reports\",\n            roles: [\n                \"case_manager\",\n                \"system_admin\",\n                \"finance_manager\"\n            ]\n        },\n        {\n            title: t(\"manage_distributions\"),\n            description: t(\"Configure distribution categories and amounts\"),\n            icon: _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            href: \"/distributions\",\n            roles: [\n                \"system_admin\",\n                \"finance_manager\"\n            ]\n        },\n        {\n            title: t(\"User Management\"),\n            description: t(\"Manage system users and permissions\"),\n            icon: _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            href: \"/admin/users\",\n            roles: [\n                \"system_admin\"\n            ]\n        },\n        {\n            title: t(\"System Settings\"),\n            description: t(\"Configure system preferences\"),\n            icon: _barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            href: \"/admin/settings\",\n            roles: [\n                \"system_admin\"\n            ]\n        }\n    ];\n    const userRole = session?.user?.role;\n    const filteredActions = actions.filter((action)=>!action.roles || action.roles.includes(userRole));\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"card-enhanced border-0 bg-card/80 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                className: \"pb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                    className: \"text-xl font-semibold flex items-center space-x-2 rtl:space-x-reverse\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-8 rounded-lg bg-gradient-primary flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CreditCard_FileText_Search_Settings_UserPlus_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4 text-primary-foreground\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: t(\"quick_actions\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                            lineNumber: 78,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                    children: filteredActions.map((action, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group relative overflow-hidden rounded-xl border border-border/50 bg-card/50 p-4 transition-all duration-200 hover:shadow-medium hover:border-primary/20 hover:-translate-y-1 cursor-pointer\",\n                            onClick: ()=>router.push(action.href),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3 rtl:space-x-reverse\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-10 w-10 rounded-lg bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                                lineNumber: 91,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 min-w-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-sm text-foreground group-hover:text-primary transition-colors\",\n                                                    children: action.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-muted-foreground mt-1 leading-relaxed\",\n                                                    children: action.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                                    lineNumber: 97,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\quick-actions.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(QuickActions, \"4EMYwZlkjzD8B7VXvH8TCPqVQtQ=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_2__.useTranslation,\n        next_auth_react__WEBPACK_IMPORTED_MODULE_3__.useSession,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/quick-actions.tsx\n"));

/***/ })

});