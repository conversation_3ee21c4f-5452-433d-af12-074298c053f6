"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./lib/i18n.ts":
/*!*********************!*\
  !*** ./lib/i18n.ts ***!
  \*********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var i18next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! i18next */ \"(app-pages-browser)/./node_modules/i18next/dist/esm/i18next.js\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! i18next-browser-languagedetector */ \"(app-pages-browser)/./node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js\");\n\n\n\nconst resources = {\n    ar: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"لوحة التحكم\",\n            \"profile\": \"الملف الشخصي\",\n            \"requests\": \"الطلبات\",\n            \"tasks\": \"المهام\",\n            \"reports\": \"التقارير\",\n            \"settings\": \"الإعدادات\",\n            \"logout\": \"تسجيل الخروج\",\n            // Authentication\n            \"login\": \"تسجيل الدخول\",\n            \"register\": \"إنشاء حساب\",\n            \"email\": \"البريد الإلكتروني\",\n            \"password\": \"كلمة المرور\",\n            \"full_name\": \"الاسم الكامل\",\n            \"national_id\": \"رقم الهوية الوطنية\",\n            \"phone_number\": \"رقم الهاتف\",\n            \"login_tawtheeq\": \"تسجيل الدخول عبر توثيق\",\n            \"login_success\": \"تم تسجيل الدخول بنجاح\",\n            \"login_error\": \"خطأ في البيانات المدخلة\",\n            // Dashboard Enhancements\n            \"welcome\": \"مرحباً\",\n            \"overview_status\": \"نظرة عامة على حالة\",\n            \"system_overview\": \"النظام\",\n            \"your_requests\": \"طلباتك\",\n            \"total_beneficiaries\": \"إجمالي المستفيدين\",\n            \"total_zakat_distributed\": \"إجمالي الزكاة الموزعة\",\n            \"pending_applications\": \"الطلبات المعلقة\",\n            \"active_distributions\": \"التوزيعات النشطة\",\n            \"quick_actions\": \"الإجراءات السريعة\",\n            \"register_new_beneficiary\": \"تسجيل مستفيد جديد\",\n            \"search_beneficiaries\": \"البحث عن المستفيدين\",\n            \"generate_report\": \"إنشاء تقرير\",\n            \"manage_distributions\": \"إدارة التوزيعات\",\n            \"recent_activity\": \"النشاط الأخير\",\n            \"distribution_overview\": \"نظرة عامة على التوزيع\",\n            \"monthly_distributions\": \"التوزيعات الشهرية\",\n            \"beneficiary_categories\": \"فئات المستفيدين\",\n            \"view_all\": \"عرض الكل\",\n            \"this_month\": \"هذا الشهر\",\n            \"last_month\": \"الشهر الماضي\",\n            \"last_week\": \"الأسبوع الماضي\",\n            \"this_week\": \"هذا الأسبوع\",\n            \"increase\": \"زيادة\",\n            \"decrease\": \"انخفاض\",\n            \"from\": \"من\",\n            \"sar\": \"ريال سعودي\",\n            \"beneficiaries\": \"مستفيدين\",\n            \"applications\": \"طلبات\",\n            \"distributions\": \"توزيعات\",\n            \"completed\": \"مكتمل\",\n            \"pending\": \"معلق\",\n            \"warning\": \"تحذير\",\n            // Profile\n            \"personal_profile\": \"الملف الشخصي\",\n            \"basic_info\": \"المعلومات الأساسية\",\n            \"family_info\": \"بيانات الأسرة\",\n            \"employment_info\": \"بيانات العمل\",\n            \"financial_info\": \"المعلومات المالية\",\n            \"marital_status\": \"الحالة الاجتماعية\",\n            \"single\": \"أعزب\",\n            \"married\": \"متزوج\",\n            \"divorced\": \"مطلق\",\n            \"widowed\": \"أرمل\",\n            // Assistance Requests\n            \"assistance_request\": \"طلب المساعدة\",\n            \"new_request\": \"طلب جديد\",\n            \"request_type\": \"نوع المساعدة\",\n            \"requested_amount\": \"المبلغ المطلوب\",\n            \"approved_amount\": \"المبلغ المعتمد\",\n            \"description\": \"الوصف\",\n            \"attach_documents\": \"إرفاق المستندات\",\n            \"submit_request\": \"إرسال الطلب\",\n            \"requests_subtitle_applicant\": \"إدارة طلبات المساعدة الخاصة بك\",\n            \"requests_subtitle_reviewer\": \"مراجعة جميع طلبات المساعدة\",\n            \"search_requests_placeholder\": \"البحث في الطلبات...\",\n            \"request_number\": \"رقم الطلب\",\n            \"submission_date\": \"تاريخ التقديم\",\n            \"processing_stages\": \"مراحل المعالجة\",\n            \"no_requests\": \"لا توجد طلبات\",\n            \"no_requests_search\": \"لم يتم العثور على طلبات تطابق البحث\",\n            \"no_requests_applicant\": \"لم تقم بتقديم أي طلبات بعد\",\n            \"no_requests_reviewer\": \"لا توجد طلبات للمراجعة\",\n            \"submit_new_request\": \"تقديم طلب جديد\",\n            // Status\n            \"draft\": \"مسودة\",\n            \"submitted\": \"مرسل\",\n            // Workflow Stages\n            \"reception_review\": \"مراجعة الاستقبال\",\n            \"researcher_review\": \"مراجعة الباحث\",\n            \"banking_expert_review\": \"مراجعة الخبير المصرفي\",\n            \"department_head_review\": \"مراجعة رئيس القسم\",\n            \"admin_manager_review\": \"مراجعة مدير الإدارة\",\n            \"minister_review\": \"مراجعة الوزير\",\n            // Actions\n            \"approve\": \"موافقة\",\n            \"reject\": \"رفض\",\n            \"return\": \"إرجاع\",\n            \"save\": \"حفظ\",\n            \"edit\": \"تعديل\",\n            \"delete\": \"حذف\",\n            \"view\": \"عرض\",\n            \"download\": \"تحميل\",\n            \"upload\": \"رفع\",\n            // Common\n            \"loading\": \"جاري التحميل...\",\n            \"search\": \"بحث\",\n            \"filter\": \"تصفية\",\n            \"date\": \"التاريخ\",\n            \"amount\": \"المبلغ\",\n            \"status\": \"الحالة\",\n            \"notes\": \"الملاحظات\",\n            \"documents\": \"المستندات\",\n            \"history\": \"التاريخ\",\n            // Dashboard Stats\n            \"total_requests\": \"إجمالي الطلبات\",\n            \"pending_review\": \"في انتظار المراجعة\",\n            \"approved_today\": \"موافق عليها اليوم\",\n            \"rejected_today\": \"مرفوضة اليوم\",\n            \"average_processing_days\": \"متوسط أيام المعالجة\",\n            \"total_users\": \"إجمالي المستخدمين\",\n            // User Roles\n            \"zakat_applicant\": \"مقدم طلب الزكاة\",\n            \"reception_staff\": \"موظف الاستقبال\",\n            \"researcher\": \"الباحث\",\n            \"banking_expert\": \"الخبير المصرفي\",\n            \"department_head\": \"رئيس القسم\",\n            \"admin_manager\": \"مدير الإدارة\",\n            \"minister\": \"الوزير\",\n            \"system_admin\": \"مسؤول النظام\",\n            // Admin Navigation\n            \"User Management\": \"إدارة المستخدمين\",\n            \"Assistance Types\": \"أنواع المساعدات\",\n            \"System Settings\": \"إعدادات النظام\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"إدارة المستفيدين\",\n            \"beneficiary_management_desc\": \"إدارة وتتبع المستفيدين من الزكاة والمساعدات\",\n            \"beneficiary_registration\": \"تسجيل مستفيد جديد\",\n            \"beneficiary_registration_desc\": \"إضافة مستفيد جديد إلى نظام إدارة الزكاة\",\n            \"beneficiary_list\": \"قائمة المستفيدين\",\n            \"beneficiary_profile\": \"الملف الشخصي للمستفيد\",\n            \"beneficiary_details\": \"تفاصيل المستفيد\",\n            \"beneficiary_not_found\": \"المستفيد غير موجود\",\n            \"beneficiary_not_found_desc\": \"لم يتم العثور على المستفيد المطلوب\",\n            \"back_to_beneficiaries\": \"العودة إلى قائمة المستفيدين\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"المعتمدين\",\n            \"under_review_beneficiaries\": \"قيد المراجعة\",\n            \"total_distributions\": \"إجمالي التوزيعات\",\n            \"average_distribution\": \"متوسط التوزيع\",\n            \"pending_verification_count\": \"في انتظار التحقق\",\n            \"needs_review\": \"يحتاج إلى مراجعة\",\n            \"of_total\": \"من الإجمالي\",\n            \"per_beneficiary\": \"للمستفيد\",\n            // Search and Filters\n            \"search_and_filter\": \"البحث والتصفية\",\n            \"search_placeholder\": \"البحث بالاسم، رقم الهوية، أو رقم الهاتف...\",\n            \"filter_by_status\": \"تصفية بالحالة\",\n            \"filter_by_category\": \"تصفية بالفئة\",\n            \"all_statuses\": \"جميع الحالات\",\n            \"all_categories\": \"جميع الفئات\",\n            \"no_results_found\": \"لا توجد نتائج مطابقة لمعايير البحث\",\n            \"showing_results\": \"عرض {{count}} من أصل {{total}} مستفيد\",\n            \"export\": \"تصدير\",\n            // Beneficiary Status\n            \"suspended\": \"معلق\",\n            \"inactive\": \"غير نشط\",\n            // Zakat Categories\n            \"fuqara\": \"الفقراء\",\n            \"masakin\": \"المساكين\",\n            \"amilin\": \"العاملين عليها\",\n            \"muallafah\": \"المؤلفة قلوبهم\",\n            \"riqab\": \"في الرقاب\",\n            \"gharimin\": \"الغارمين\",\n            \"fisabilillah\": \"في سبيل الله\",\n            \"ibnus_sabil\": \"ابن السبيل\",\n            \"primary_category\": \"الفئة الأساسية\",\n            // Table Headers\n            \"name\": \"الاسم\",\n            \"total_received\": \"إجمالي المستلم\",\n            // Actions\n            \"view_profile\": \"عرض الملف الشخصي\",\n            \"manage_case\": \"إدارة الحالة\",\n            \"generate_voucher\": \"إنشاء قسيمة\",\n            \"back\": \"العودة\",\n            // Profile Tabs\n            \"overview\": \"نظرة عامة\",\n            \"personal_details\": \"البيانات الشخصية\",\n            \"eligibility_verification\": \"الأهلية والتحقق\",\n            \"case_management\": \"إدارة الحالة\",\n            \"distribution_history\": \"سجل التوزيعات\",\n            \"family\": \"الأسرة\",\n            // Profile Details\n            \"eligibility_score\": \"نقاط الأهلية\",\n            \"high_score\": \"درجة عالية\",\n            \"family_size\": \"حجم الأسرة\",\n            \"dependents\": \"معالين\",\n            \"last_distribution\": \"آخر توزيع\",\n            \"no_distribution\": \"لا يوجد\",\n            \"not_distributed_yet\": \"لم يتم التوزيع بعد\",\n            // Contact Information\n            \"contact_info\": \"معلومات الاتصال\",\n            \"account_status\": \"حالة الحساب\",\n            \"current_status\": \"الحالة الحالية\",\n            \"registration_date\": \"تاريخ التسجيل\",\n            \"next_review\": \"المراجعة القادمة\",\n            // Personal Information\n            \"personal_information\": \"البيانات الشخصية\",\n            \"basic_information\": \"المعلومات الأساسية\",\n            \"name_arabic\": \"الاسم بالعربية\",\n            \"name_english\": \"الاسم بالإنجليزية\",\n            \"date_of_birth\": \"تاريخ الميلاد\",\n            \"gender\": \"الجنس\",\n            \"male\": \"ذكر\",\n            \"female\": \"أنثى\",\n            // Family Members\n            \"family_members\": \"أفراد الأسرة\",\n            \"no_family_info\": \"لا توجد معلومات عن أفراد الأسرة\",\n            \"dependent\": \"معال\",\n            \"special_needs\": \"احتياجات خاصة\",\n            \"relationship\": \"القرابة\",\n            \"age\": \"العمر\",\n            \"years\": \"سنة\",\n            \"son\": \"ابن\",\n            \"daughter\": \"ابنة\",\n            \"mother\": \"أم\",\n            \"father\": \"أب\",\n            // Documents\n            \"no_documents\": \"لا توجد مستندات مرفوعة\",\n            \"verified\": \"محقق\",\n            \"pending_verification\": \"في انتظار التحقق\",\n            \"upload_date\": \"تاريخ الرفع\",\n            // Coming Soon\n            \"coming_soon\": \"قريباً\",\n            \"under_development\": \"قيد التطوير\",\n            \"registration_form_coming\": \"نموذج التسجيل قيد التطوير\",\n            \"registration_form_desc\": \"سيتم إضافة نموذج تسجيل المستفيدين الجدد قريباً\",\n            \"will_include_features\": \"سيتضمن النموذج الميزات التالية:\",\n            \"multi_step_form\": \"نموذج متعدد الخطوات مع مؤشر التقدم\",\n            \"dual_language_input\": \"إدخال البيانات الشخصية بالعربية والإنجليزية\",\n            \"zakat_categories_selection\": \"اختيار فئات الزكاة الثمانية\",\n            \"document_upload\": \"رفع المستندات المطلوبة\",\n            \"data_validation\": \"التحقق من صحة البيانات\",\n            \"duplicate_detection\": \"كشف التكرار التلقائي\",\n            // System Information\n            \"system_name\": \"نظام إدارة الزكاة\",\n            \"system_description\": \"نظام شامل لإدارة طلبات الزكاة والمساعدات\",\n            // Dashboard\n            \"role\": \"الدور\",\n            \"dashboard_subtitle\": \"نظرة عامة شاملة على النظام\",\n            // Quick Actions Descriptions\n            \"Add a new beneficiary to the system\": \"إضافة مستفيد جديد إلى النظام\",\n            \"Find and manage existing beneficiaries\": \"البحث عن المستفيدين الحاليين وإدارتهم\",\n            \"Create distribution and analytics reports\": \"إنشاء تقارير التوزيع والتحليلات\",\n            \"Configure distribution categories and amounts\": \"تكوين فئات ومبالغ التوزيع\",\n            \"Manage system users and permissions\": \"إدارة مستخدمي النظام والصلاحيات\",\n            \"Configure system preferences\": \"تكوين تفضيلات النظام\",\n            // Recent Activity\n            \"New beneficiary registered\": \"تم تسجيل مستفيد جديد\",\n            \"Application approved\": \"تم الموافقة على الطلب\",\n            \"Zakat distributed\": \"تم توزيع الزكاة\",\n            \"Pending review\": \"في انتظار المراجعة\",\n            \"Ahmed Mohammed Al-Rashid has been registered\": \"تم تسجيل أحمد محمد الراشد\",\n            \"Fatima Al-Zahra application approved for Zakat distribution\": \"تم الموافقة على طلب فاطمة الزهراء لتوزيع الزكاة\",\n            \"5,000 SAR distributed to 10 beneficiaries\": \"تم توزيع 5,000 ريال سعودي على 10 مستفيدين\",\n            \"3 applications require case manager review\": \"3 طلبات تتطلب مراجعة مدير الحالة\",\n            \"Reception Staff\": \"موظف الاستقبال\",\n            \"Case Manager\": \"مدير الحالة\",\n            \"Finance Manager\": \"مدير المالية\",\n            \"System\": \"النظام\",\n            // Authentication Messages\n            \"create_new_account\": \"إنشاء حساب جديد\",\n            \"sign_in_to_account\": \"تسجيل الدخول إلى حسابك\",\n            \"choose_login_method\": \"اختر طريقة تسجيل الدخول المناسبة\",\n            \"verifying\": \"جاري التحقق...\",\n            \"error\": \"خطأ\",\n            \"passwords_not_match\": \"كلمات المرور غير متطابقة\",\n            \"account_created_success\": \"تم إنشاء الحساب بنجاح\",\n            \"wait_admin_approval\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            \"account_creation_error\": \"خطأ في إنشاء الحساب\",\n            \"unexpected_error\": \"حدث خطأ غير متوقع\",\n            \"error_during_creation\": \"حدث خطأ أثناء إنشاء الحساب\",\n            \"invalid_credentials\": \"البيانات المدخلة غير صحيحة\",\n            \"welcome_to_system\": \"مرحباً بك في نظام إدارة الزكاة\",\n            \"error_during_login\": \"حدث خطأ أثناء تسجيل الدخول\",\n            // Account Status\n            \"account_pending_approval\": \"حسابك في انتظار الموافقة\",\n            \"wait_admin_approval_desc\": \"يرجى انتظار موافقة الإدارة على حسابك\",\n            // Dashboard\n            \"assigned_tasks\": \"المهام المخصصة لك\",\n            \"total_requests_desc\": \"إجمالي الطلبات\",\n            \"pending_review_desc\": \"في انتظار المراجعة\",\n            \"approved_today_desc\": \"موافق عليها اليوم\",\n            \"avg_processing_days_desc\": \"متوسط أيام المعالجة\",\n            // Reports\n            \"no_reports_access\": \"ليس لديك صلاحية للوصول إلى التقارير\",\n            \"monthly_report\": \"التقرير الشهري\",\n            \"monthly_stats_desc\": \"إحصائيات الطلبات والموافقات الشهرية\",\n            \"requests_label\": \"الطلبات\",\n            \"approved_label\": \"موافق\",\n            \"rejected_label\": \"مرفوض\",\n            // Requests\n            \"back_button\": \"العودة\",\n            \"request_details\": \"تفاصيل الطلب\",\n            \"download_decision\": \"تحميل القرار\",\n            // Gender and Personal Info\n            \"gender_label\": \"الجنس\",\n            \"male_label\": \"ذكر\",\n            \"female_label\": \"أنثى\",\n            \"marital_status_label\": \"الحالة الاجتماعية\",\n            \"married_label\": \"متزوج\",\n            \"single_label\": \"أعزب\",\n            \"divorced_label\": \"مطلق\",\n            \"widowed_label\": \"أرمل\",\n            // Common UI Text\n            \"or\": \"أو\",\n            \"no_account\": \"ليس لديك حساب؟\",\n            \"demo_accounts\": \"حسابات تجريبية:\",\n            \"applicant\": \"مقدم طلب:\",\n            \"staff_member\": \"موظف:\",\n            // Access Control\n            \"access_denied\": \"غير مصرح\",\n            \"no_beneficiary_access\": \"ليس لديك صلاحية للوصول إلى إدارة المستفيدين\",\n            \"no_registration_access\": \"ليس لديك صلاحية لتسجيل مستفيدين جدد\"\n        }\n    },\n    en: {\n        translation: {\n            // Navigation\n            \"dashboard\": \"Dashboard\",\n            \"profile\": \"Profile\",\n            \"requests\": \"Requests\",\n            \"tasks\": \"Tasks\",\n            \"reports\": \"Reports\",\n            \"settings\": \"Settings\",\n            \"logout\": \"Logout\",\n            // Authentication\n            \"login\": \"Login\",\n            \"register\": \"Register\",\n            \"email\": \"Email\",\n            \"password\": \"Password\",\n            \"full_name\": \"Full Name\",\n            \"national_id\": \"National ID\",\n            \"phone_number\": \"Phone Number\",\n            \"login_tawtheeq\": \"Login with Tawtheeq\",\n            \"login_success\": \"Login successful\",\n            \"login_error\": \"Invalid credentials\",\n            // Dashboard Enhancements\n            \"welcome\": \"Welcome\",\n            \"overview_status\": \"Overview of\",\n            \"system_overview\": \"system status\",\n            \"your_requests\": \"your requests\",\n            \"total_beneficiaries\": \"Total Beneficiaries\",\n            \"total_zakat_distributed\": \"Total Zakat Distributed\",\n            \"pending_applications\": \"Pending Applications\",\n            \"active_distributions\": \"Active Distributions\",\n            \"quick_actions\": \"Quick Actions\",\n            \"register_new_beneficiary\": \"Register New Beneficiary\",\n            \"search_beneficiaries\": \"Search Beneficiaries\",\n            \"generate_report\": \"Generate Report\",\n            \"manage_distributions\": \"Manage Distributions\",\n            \"recent_activity\": \"Recent Activity\",\n            \"distribution_overview\": \"Distribution Overview\",\n            \"monthly_distributions\": \"Monthly Distributions\",\n            \"beneficiary_categories\": \"Beneficiary Categories\",\n            \"view_all\": \"View All\",\n            \"this_month\": \"This Month\",\n            \"last_month\": \"Last Month\",\n            \"last_week\": \"Last Week\",\n            \"this_week\": \"This Week\",\n            \"increase\": \"increase\",\n            \"decrease\": \"decrease\",\n            \"from\": \"from\",\n            \"sar\": \"SAR\",\n            \"beneficiaries\": \"beneficiaries\",\n            \"applications\": \"applications\",\n            \"distributions\": \"distributions\",\n            \"completed\": \"Completed\",\n            \"pending\": \"Pending\",\n            \"warning\": \"Warning\",\n            // Profile\n            \"personal_profile\": \"Personal Profile\",\n            \"basic_info\": \"Basic Information\",\n            \"family_info\": \"Family Information\",\n            \"employment_info\": \"Employment Information\",\n            \"financial_info\": \"Financial Information\",\n            \"marital_status\": \"Marital Status\",\n            \"single\": \"Single\",\n            \"married\": \"Married\",\n            \"divorced\": \"Divorced\",\n            \"widowed\": \"Widowed\",\n            // Assistance Requests\n            \"assistance_request\": \"Assistance Request\",\n            \"new_request\": \"New Request\",\n            \"request_type\": \"Request Type\",\n            \"requested_amount\": \"Requested Amount\",\n            \"description\": \"Description\",\n            \"attach_documents\": \"Attach Documents\",\n            \"submit_request\": \"Submit Request\",\n            // Status\n            \"draft\": \"Draft\",\n            \"submitted\": \"Submitted\",\n            // Workflow Stages\n            \"reception_review\": \"Reception Review\",\n            \"researcher_review\": \"Researcher Review\",\n            \"banking_expert_review\": \"Banking Expert Review\",\n            \"department_head_review\": \"Department Head Review\",\n            \"admin_manager_review\": \"Admin Manager Review\",\n            \"minister_review\": \"Minister Review\",\n            // Actions\n            \"approve\": \"Approve\",\n            \"reject\": \"Reject\",\n            \"return\": \"Return\",\n            \"save\": \"Save\",\n            \"edit\": \"Edit\",\n            \"delete\": \"Delete\",\n            \"view\": \"View\",\n            \"download\": \"Download\",\n            \"upload\": \"Upload\",\n            // Common\n            \"loading\": \"Loading...\",\n            \"search\": \"Search\",\n            \"filter\": \"Filter\",\n            \"date\": \"Date\",\n            \"amount\": \"Amount\",\n            \"status\": \"Status\",\n            \"notes\": \"Notes\",\n            \"documents\": \"Documents\",\n            \"history\": \"History\",\n            // Dashboard Stats\n            \"total_requests\": \"Total Requests\",\n            \"pending_review\": \"Pending Review\",\n            \"approved_today\": \"Approved Today\",\n            \"rejected_today\": \"Rejected Today\",\n            \"average_processing_days\": \"Avg. Processing Days\",\n            \"total_users\": \"Total Users\",\n            // User Roles\n            \"zakat_applicant\": \"Zakat Applicant\",\n            \"reception_staff\": \"Reception Staff\",\n            \"researcher\": \"Researcher\",\n            \"banking_expert\": \"Banking Expert\",\n            \"department_head\": \"Department Head\",\n            \"admin_manager\": \"Administration Manager\",\n            \"minister\": \"Minister\",\n            \"system_admin\": \"System Administrator\",\n            // Admin Navigation\n            \"User Management\": \"User Management\",\n            \"Assistance Types\": \"Assistance Types\",\n            \"System Settings\": \"System Settings\",\n            // Beneficiary Management\n            \"beneficiary_management\": \"Beneficiary Management\",\n            \"beneficiary_management_desc\": \"Manage and track Zakat and assistance beneficiaries\",\n            \"beneficiary_registration\": \"New Beneficiary Registration\",\n            \"beneficiary_registration_desc\": \"Add a new beneficiary to the Zakat management system\",\n            \"beneficiary_list\": \"Beneficiary List\",\n            \"beneficiary_profile\": \"Beneficiary Profile\",\n            \"beneficiary_details\": \"Beneficiary Details\",\n            \"beneficiary_not_found\": \"Beneficiary Not Found\",\n            \"beneficiary_not_found_desc\": \"The requested beneficiary could not be found\",\n            \"back_to_beneficiaries\": \"Back to Beneficiaries List\",\n            // Beneficiary Stats\n            \"approved_beneficiaries\": \"Approved\",\n            \"under_review_beneficiaries\": \"Under Review\",\n            \"total_distributions\": \"Total Distributions\",\n            \"average_distribution\": \"Average Distribution\",\n            \"pending_verification_count\": \"Pending Verification\",\n            \"needs_review\": \"Needs Review\",\n            \"of_total\": \"of Total\",\n            \"per_beneficiary\": \"per Beneficiary\",\n            // Search and Filters\n            \"search_and_filter\": \"Search and Filter\",\n            \"search_placeholder\": \"Search by name, national ID, or phone number...\",\n            \"filter_by_status\": \"Filter by Status\",\n            \"filter_by_category\": \"Filter by Category\",\n            \"all_statuses\": \"All Statuses\",\n            \"all_categories\": \"All Categories\",\n            \"no_results_found\": \"No results found matching search criteria\",\n            \"showing_results\": \"Showing {{count}} of {{total}} beneficiaries\",\n            \"export\": \"Export\",\n            // Beneficiary Status\n            \"pending_verification\": \"Pending Verification\",\n            \"under_review\": \"Under Review\",\n            \"approved\": \"Approved\",\n            \"rejected\": \"Rejected\",\n            \"suspended\": \"Suspended\",\n            \"inactive\": \"Inactive\",\n            // Zakat Categories\n            \"fuqara\": \"The Poor\",\n            \"masakin\": \"The Needy\",\n            \"amilin\": \"Zakat Administrators\",\n            \"muallafah\": \"Those whose hearts are reconciled\",\n            \"riqab\": \"To free slaves/captives\",\n            \"gharimin\": \"Those in debt\",\n            \"fisabilillah\": \"In the cause of Allah\",\n            \"ibnus_sabil\": \"The wayfarer/traveler\",\n            \"primary_category\": \"Primary Category\",\n            // Table Headers\n            \"name\": \"Name\",\n            \"total_received\": \"Total Received\",\n            // Actions\n            \"view_profile\": \"View Profile\",\n            \"manage_case\": \"Manage Case\",\n            \"generate_voucher\": \"Generate Voucher\",\n            \"back\": \"Back\",\n            // Profile Tabs\n            \"overview\": \"Overview\",\n            \"personal_details\": \"Personal Details\",\n            \"eligibility_verification\": \"Eligibility & Verification\",\n            \"case_management\": \"Case Management\",\n            \"distribution_history\": \"Distribution History\",\n            \"family\": \"Family\",\n            // Profile Details\n            \"eligibility_score\": \"Eligibility Score\",\n            \"high_score\": \"High Score\",\n            \"family_size\": \"Family Size\",\n            \"dependents\": \"Dependents\",\n            \"last_distribution\": \"Last Distribution\",\n            \"no_distribution\": \"None\",\n            \"not_distributed_yet\": \"Not distributed yet\",\n            // Contact Information\n            \"contact_info\": \"Contact Information\",\n            \"account_status\": \"Account Status\",\n            \"current_status\": \"Current Status\",\n            \"registration_date\": \"Registration Date\",\n            \"next_review\": \"Next Review\",\n            // Personal Information\n            \"personal_information\": \"Personal Information\",\n            \"basic_information\": \"Basic Information\",\n            \"name_arabic\": \"Name in Arabic\",\n            \"name_english\": \"Name in English\",\n            \"date_of_birth\": \"Date of Birth\",\n            \"gender\": \"Gender\",\n            \"male\": \"Male\",\n            \"female\": \"Female\",\n            // Family Members\n            \"family_members\": \"Family Members\",\n            \"no_family_info\": \"No family information available\",\n            \"dependent\": \"Dependent\",\n            \"special_needs\": \"Special Needs\",\n            \"relationship\": \"Relationship\",\n            \"age\": \"Age\",\n            \"years\": \"years\",\n            \"son\": \"Son\",\n            \"daughter\": \"Daughter\",\n            \"mother\": \"Mother\",\n            \"father\": \"Father\",\n            // Documents\n            \"no_documents\": \"No documents uploaded\",\n            \"verified\": \"Verified\",\n            \"upload_date\": \"Upload Date\",\n            // Coming Soon\n            \"coming_soon\": \"Coming Soon\",\n            \"under_development\": \"Under Development\",\n            \"registration_form_coming\": \"Registration Form Under Development\",\n            \"registration_form_desc\": \"New beneficiary registration form will be added soon\",\n            \"will_include_features\": \"The form will include the following features:\",\n            \"multi_step_form\": \"Multi-step form with progress indicator\",\n            \"dual_language_input\": \"Personal data input in Arabic and English\",\n            \"zakat_categories_selection\": \"Selection of eight Zakat categories\",\n            \"document_upload\": \"Required document upload\",\n            \"data_validation\": \"Data validation\",\n            \"duplicate_detection\": \"Automatic duplicate detection\",\n            // System Information\n            \"system_name\": \"Zakat Management System\",\n            \"system_description\": \"Comprehensive system for managing Zakat and assistance requests\",\n            // Dashboard\n            \"role\": \"Role\",\n            \"dashboard_subtitle\": \"Comprehensive system overview\",\n            // Quick Actions Descriptions\n            \"Add a new beneficiary to the system\": \"Add a new beneficiary to the system\",\n            \"Find and manage existing beneficiaries\": \"Find and manage existing beneficiaries\",\n            \"Create distribution and analytics reports\": \"Create distribution and analytics reports\",\n            \"Configure distribution categories and amounts\": \"Configure distribution categories and amounts\",\n            \"Manage system users and permissions\": \"Manage system users and permissions\",\n            \"Configure system preferences\": \"Configure system preferences\",\n            // Recent Activity\n            \"New beneficiary registered\": \"New beneficiary registered\",\n            \"Application approved\": \"Application approved\",\n            \"Zakat distributed\": \"Zakat distributed\",\n            \"Pending review\": \"Pending review\",\n            \"Ahmed Mohammed Al-Rashid has been registered\": \"Ahmed Mohammed Al-Rashid has been registered\",\n            \"Fatima Al-Zahra application approved for Zakat distribution\": \"Fatima Al-Zahra application approved for Zakat distribution\",\n            \"5,000 SAR distributed to 10 beneficiaries\": \"5,000 SAR distributed to 10 beneficiaries\",\n            \"3 applications require case manager review\": \"3 applications require case manager review\",\n            \"Reception Staff\": \"Reception Staff\",\n            \"Case Manager\": \"Case Manager\",\n            \"Finance Manager\": \"Finance Manager\",\n            \"System\": \"System\",\n            // Authentication Messages\n            \"create_new_account\": \"Create new account\",\n            \"sign_in_to_account\": \"Sign in to your account\",\n            \"choose_login_method\": \"Choose the appropriate login method\",\n            \"verifying\": \"Verifying...\",\n            \"error\": \"Error\",\n            \"passwords_not_match\": \"Passwords do not match\",\n            \"account_created_success\": \"Account created successfully\",\n            \"wait_admin_approval\": \"Please wait for admin approval of your account\",\n            \"account_creation_error\": \"Error creating account\",\n            \"unexpected_error\": \"An unexpected error occurred\",\n            \"error_during_creation\": \"An error occurred during account creation\",\n            \"invalid_credentials\": \"Invalid credentials entered\",\n            \"welcome_to_system\": \"Welcome to the Zakat Management System\",\n            \"error_during_login\": \"An error occurred during login\",\n            // Account Status\n            \"account_pending_approval\": \"Your account is pending approval\",\n            \"wait_admin_approval_desc\": \"Please wait for admin approval of your account\",\n            // Dashboard\n            \"assigned_tasks\": \"tasks assigned to you\",\n            \"total_requests_desc\": \"Total requests\",\n            \"pending_review_desc\": \"Pending review\",\n            \"approved_today_desc\": \"Approved today\",\n            \"avg_processing_days_desc\": \"Average processing days\",\n            // Reports\n            \"no_reports_access\": \"You do not have permission to access reports\",\n            \"monthly_report\": \"Monthly Report\",\n            \"monthly_stats_desc\": \"Monthly statistics of requests and approvals\",\n            \"requests_label\": \"Requests\",\n            \"approved_label\": \"Approved\",\n            \"rejected_label\": \"Rejected\",\n            // Requests\n            \"back_button\": \"Back\",\n            \"request_details\": \"Request Details\",\n            \"download_decision\": \"Download Decision\",\n            // Gender and Personal Info\n            \"gender_label\": \"Gender\",\n            \"male_label\": \"Male\",\n            \"female_label\": \"Female\",\n            \"marital_status_label\": \"Marital Status\",\n            \"married_label\": \"Married\",\n            \"single_label\": \"Single\",\n            \"divorced_label\": \"Divorced\",\n            \"widowed_label\": \"Widowed\",\n            // Common UI Text\n            \"or\": \"or\",\n            \"no_account\": \"Don't have an account?\",\n            \"demo_accounts\": \"Demo Accounts:\",\n            \"applicant\": \"Applicant:\",\n            \"staff_member\": \"Staff Member:\",\n            // Access Control\n            \"access_denied\": \"Access Denied\",\n            \"no_beneficiary_access\": \"You do not have permission to access beneficiary management\",\n            \"no_registration_access\": \"You do not have permission to register new beneficiaries\"\n        }\n    }\n};\n// Initialize i18n immediately with resources\ni18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"].use(i18next_browser_languagedetector__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).use(react_i18next__WEBPACK_IMPORTED_MODULE_1__.initReactI18next).init({\n    resources,\n    fallbackLng: \"ar\",\n    lng: \"ar\",\n    debug: \"development\" === \"development\",\n    detection: {\n        order: [\n            \"localStorage\",\n            \"navigator\",\n            \"htmlTag\"\n        ],\n        caches: [\n            \"localStorage\"\n        ]\n    },\n    interpolation: {\n        escapeValue: false\n    }\n});\n/* harmony default export */ __webpack_exports__[\"default\"] = (i18next__WEBPACK_IMPORTED_MODULE_0__[\"default\"]);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n.ts\n"));

/***/ })

});