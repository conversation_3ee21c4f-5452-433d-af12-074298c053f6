"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./components/dashboard/recent-activity.tsx":
/*!**************************************************!*\
  !*** ./components/dashboard/recent-activity.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecentActivity: function() { return /* binding */ RecentActivity; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var react_i18next__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-i18next */ \"(app-pages-browser)/./node_modules/react-i18next/dist/es/index.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/ar.mjs\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/date-fns/locale/en-US.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user-plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,CreditCard,UserPlus!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ RecentActivity auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mock data - replace with real data from your mock-data.ts\nconst getMockActivities = (t)=>[\n        {\n            id: \"1\",\n            type: \"registration\",\n            title: t(\"New beneficiary registered\"),\n            description: t(\"Ahmed Mohammed Al-Rashid has been registered\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 30),\n            status: \"completed\",\n            user: t(\"Reception Staff\")\n        },\n        {\n            id: \"2\",\n            type: \"approval\",\n            title: t(\"Application approved\"),\n            description: t(\"Fatima Al-Zahra application approved for Zakat distribution\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n            status: \"completed\",\n            user: t(\"Case Manager\")\n        },\n        {\n            id: \"3\",\n            type: \"distribution\",\n            title: t(\"Zakat distributed\"),\n            description: t(\"5,000 SAR distributed to 10 beneficiaries\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4),\n            status: \"completed\",\n            user: t(\"Finance Manager\")\n        },\n        {\n            id: \"4\",\n            type: \"review\",\n            title: t(\"Pending review\"),\n            description: t(\"3 applications require case manager review\"),\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6),\n            status: \"pending\",\n            user: t(\"System\")\n        }\n    ];\nfunction RecentActivity() {\n    _s();\n    const { t, i18n } = (0,react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation)();\n    const isArabic = i18n.language === \"ar\";\n    const getActivityIcon = (type)=>{\n        switch(type){\n            case \"registration\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n            case \"approval\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n            case \"distribution\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"];\n            case \"review\":\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n            default:\n                return _barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"completed\":\n                return \"default\";\n            case \"pending\":\n                return \"secondary\";\n            case \"warning\":\n                return \"destructive\";\n            default:\n                return \"secondary\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"card-enhanced border-0 bg-card/80 backdrop-blur-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardHeader, {\n                className: \"flex flex-row items-center justify-between pb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardTitle, {\n                        className: \"text-xl font-semibold flex items-center space-x-2 rtl:space-x-reverse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-8 w-8 rounded-lg bg-gradient-success flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_CreditCard_UserPlus_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 text-success-foreground\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: t(\"recent_activity\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"sm\",\n                        className: \"text-primary hover:text-primary-light hover:bg-primary/10\",\n                        children: t(\"view_all\")\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: mockActivities.map((activity)=>{\n                        const Icon = getActivityIcon(activity.type);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"group flex items-start space-x-3 rtl:space-x-reverse p-3 rounded-lg hover:bg-accent/50 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-10 w-10 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                            className: \"h-5 w-5 text-primary\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 min-w-0\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-semibold text-foreground\",\n                                                    children: activity.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                                                    variant: getStatusColor(activity.status),\n                                                    className: `text-xs font-medium ${activity.status === \"completed\" ? \"bg-success/20 text-success border-success/30\" : activity.status === \"pending\" ? \"bg-warning/20 text-warning border-warning/30\" : \"bg-destructive/20 text-destructive border-destructive/30\"}`,\n                                                    children: t(activity.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-muted-foreground mb-2\",\n                                            children: activity.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_10__.formatDistanceToNow)(activity.timestamp, {\n                                                    addSuffix: true,\n                                                    locale: isArabic ? date_fns_locale__WEBPACK_IMPORTED_MODULE_11__.ar : date_fns_locale__WEBPACK_IMPORTED_MODULE_12__.enUS\n                                                }),\n                                                activity.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mx-1 text-primary\",\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 137,\n                                                    columnNumber: 23\n                                                }, this),\n                                                activity.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: activity.user\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, activity.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\VCode projects\\\\zakat-deepagent\\\\zakat_management_system\\\\app\\\\components\\\\dashboard\\\\recent-activity.tsx\",\n        lineNumber: 91,\n        columnNumber: 5\n    }, this);\n}\n_s(RecentActivity, \"OZwazanA59tbNDUkc8lMSmTHj9Q=\", false, function() {\n    return [\n        react_i18next__WEBPACK_IMPORTED_MODULE_4__.useTranslation\n    ];\n});\n_c = RecentActivity;\nvar _c;\n$RefreshReg$(_c, \"RecentActivity\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/dashboard/recent-activity.tsx\n"));

/***/ })

});